"use client";
import { useEffect, useMemo, useState } from "react";
import {
  Box, Button, Card, CardBody,
  HStack, Input, List, ListItem, SimpleGrid, Skeleton, SkeletonText,
  Stack, Text, useBreakpointValue, useDisclosure, Link, ScrollArea, EmptyState, VStack, Icon, Pagination,
  Drawer, DrawerBody, DrawerContent, DrawerHeader
} from "@chakra-ui/react";
import { HiSearch, HiExternalLink } from "react-icons/hi";
import JobDetail from "./JobDetail";
import { Job, fetchJobs, makeSlug } from "@/lib/jobs";
import NextLink from "next/link";

const TAGS = ["tanár", "ügyintéző", "orvos", "mérnök", "informatikus"];

export default function Jobs() {
  const [items, setItems] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [q, setQ] = useState("");
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const [selected, setSelected] = useState<Job | null>(null);
  const isMobile = useBreakpointValue({ base: true, md: false });
  const drawer = useDisclosure();

  useEffect(() => {
    let mounted = true;
    fetchJobs().then((list) => {
      if (!mounted) return;
      setItems(list);
      setLoading(false);
    });
    return () => { mounted = false };
  }, []);

  useEffect(() => { setPage(1); }, [q]);

  const filtered = useMemo(() => {
    const qq = q.trim().toLowerCase();
    if (!qq) return items;
    return items.filter(it =>
      it.title.toLowerCase().includes(qq) ||
      it.companyname?.toLowerCase().includes(qq) ||
      it.address?.toLowerCase().includes(qq) ||
      it.description?.toLowerCase().includes(qq)
    );
  }, [items, q]);

  const totalPages = Math.max(1, Math.ceil(filtered.length / pageSize));
  const paged = filtered.slice((page - 1) * pageSize, page * pageSize);

  function openJob(job: Job) {
    if (isMobile) {
      setSelected(job);
      drawer.onOpen();
    } else {
      setSelected(job);
    }
  }

  return (
    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
      <Stack>
        {/* Custom input with left icon, no InputGroup */}
        <Box position="relative">
          <Icon as={HiSearch} position="absolute" left="3" top="50%" transform="translateY(-50%)" opacity={0.6} />
          <Input pl="9" placeholder="Keresés…" value={q} onChange={(e) => setQ(e.target.value)} />
        </Box>

        <HStack wrap="wrap" gap={2}>
          {TAGS.map((t) => (<Button key={t} size="sm" variant="outline" onClick={() => setQ(t)}>{t}</Button>))}
          {q && (<Button size="sm" onClick={() => setQ("")}>Törlés</Button>)}
        </HStack>

        <Card overflow="hidden"><CardBody p={0}>
          {loading ? (
            <Stack p={4} spacing={0}>{Array.from({ length: 10 }).map((_, i) => (
              <Box key={i} p={4}><Skeleton height="16px" mb={2}/><SkeletonText noOfLines={2}/><Box borderTopWidth="1px" mt={4}/></Box>
            ))}</Stack>
          ) : filtered.length === 0 ? (
            <Box p={8}><EmptyState.Root><EmptyState.Content><VStack textAlign="center">
              <EmptyState.Title>Nincs találat</EmptyState.Title>
              <EmptyState.Description>Próbálj más kulcsszót vagy távolítsd el a szűrőket.</EmptyState.Description>
            </VStack></EmptyState.Content></EmptyState.Root></Box>
          ) : (
            <ScrollArea type="always" maxH={{ base: "auto", md: "70vh" }}>
              <List>{paged.map((job, idx) => (
                <Box key={job.id} _hover={{ bg: "gray.50" }} borderTopWidth={idx === 0 ? 0 : "1px"}>
                  <ListItem p={4} onClick={() => openJob(job)} cursor="pointer">
                    <Text fontWeight="bold">{job.title}</Text>
                    <Text fontSize="sm" color="gray.600">{job.companyname} • {job.address}</Text>
                    <HStack mt={2} spacing={4}>
                      <Link as={NextLink} href={`/job/${job.id}/${makeSlug(job.title)}`} onClick={(e)=>e.stopPropagation()}>Részletek</Link>
                      <Link href={job.url} isExternal onClick={(e)=>e.stopPropagation()}><Icon as={HiExternalLink} mr={1}/>Eredeti</Link>
                    </HStack>
                  </ListItem>
                </Box>
              ))}</List>
            </ScrollArea>
          )}
        </CardBody></Card>

        {/* Default numeric pagination */}
        <Pagination.Root count={filtered.length} pageSize={pageSize} page={page} onPageChange={(e)=>setPage(e.page)}>
          <Pagination.Items />
        </Pagination.Root>
      </Stack>

      <Box display={{ base: "none", md: "block" }}>
        <ScrollArea type="always" maxH="70vh">
          {selected ? (<JobDetail job={selected}/>) : (
            <Box p={8} color="gray.500"><EmptyState.Root><EmptyState.Content><VStack textAlign="center">
              <EmptyState.Title>Válassz egy állást a listából.</EmptyState.Title>
              <EmptyState.Description>Kattints balra egy hirdetésre.</EmptyState.Description>
            </VStack></EmptyState.Content></EmptyState.Root></Box>
          )}
        </ScrollArea>
      </Box>

      {/* Bottom drawer on mobile; no DrawerCloseButton import */}
      <Drawer isOpen={drawer.isOpen} onClose={drawer.onClose} placement="bottom">
        <DrawerContent borderTopRadius="2xl">
          <DrawerHeader display="flex" justifyContent="space-between" alignItems="center">
            {selected?.title}
            <Button onClick={drawer.onClose} size="sm">Bezárás</Button>
          </DrawerHeader>
          <DrawerBody>
            {selected && (<ScrollArea type="always" maxH="calc(100dvh - 12rem)"><JobDetail job={selected}/></ScrollArea>)}
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </SimpleGrid>
  );
}
